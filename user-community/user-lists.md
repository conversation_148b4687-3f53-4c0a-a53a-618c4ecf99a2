# User Lists

User lists allow users to create custom collections of music entities (songs, releases, artists, etc.) for personal organization and sharing.

## Overview

The user list system consists of two main entities:
- **user_list**: The list container with metadata
- **user_list_item**: Individual items within lists

## user_list Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique list identifier |
| `user_id` | `i32` | Foreign key to the user who created the list |
| `name` | `String` | Display name of the list |
| `description` | `String` | Detailed description of the list's purpose |
| `is_public` | `bool` | Whether the list is publicly visible |

### Relationships

- **Belongs to**: `user` (creator of the list)
- **Has many**: `user_list_item` (items in the list)

### Visibility Settings

Lists can be configured with different visibility levels:
- **Public** (`is_public: true`): Visible to all users
- **Private** (`is_public: false`): Only visible to the creator

## user_list_item Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique item identifier |
| `user_list_id` | `i32` | Foreign key to the parent list |
| `entity_id` | `Option<i32>` | ID of the referenced entity (nullable for custom entries) |
| `entity_type` | `EntityType` | Type of entity being referenced |
| `description` | `Option<String>` | Optional custom description for this item |

### Relationships

- **Belongs to**: `user_list` (parent list)

### Entity Types

List items can reference various entity types:
- **Song**: Individual tracks
- **Release**: Albums, EPs, singles
- **Artist**: Musicians, circles, groups
- **Event**: Conventions, concerts
- **Tag**: Genre or category tags

### Custom Entries

Items with `entity_id: None` represent custom entries where users can add arbitrary content using the `description` field.

## Use Cases

### Personal Organization
- **Favorites**: Collections of preferred songs or releases
- **Playlists**: Curated listening sequences
- **Wishlist**: Items to acquire or explore later
- **Collections**: Thematic groupings (e.g., "Touhou Vocal Arrangements")

### Community Sharing
- **Recommendations**: Curated lists shared with other users
- **Collaborative Lists**: Community-maintained collections
- **Discovery**: Lists highlighting lesser-known works

### Research and Documentation
- **Discographies**: Complete works by specific artists
- **Event Catalogs**: Releases from particular conventions
- **Genre Studies**: Collections organized by musical style

## Database Schema

```sql
-- user_list table
CREATE TABLE user_list (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user(id) ON UPDATE CASCADE,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT false
);

-- user_list_item table  
CREATE TABLE user_list_item (
    id SERIAL PRIMARY KEY,
    user_list_id INTEGER NOT NULL REFERENCES user_list(id) ON UPDATE CASCADE ON DELETE CASCADE,
    entity_id INTEGER,
    entity_type entity_type NOT NULL,
    description TEXT
);
```

## API Considerations

### List Management
- Create, read, update, delete operations for lists
- Visibility controls and permission checks
- Bulk operations for managing multiple items

### Item Management
- Add/remove items from lists
- Reorder items within lists
- Batch import/export functionality

### Discovery Features
- Public list browsing
- Search within lists
- Related list suggestions

## Privacy and Permissions

### Access Control
- List creators have full control over their lists
- Public lists are readable by all users
- Private lists are only accessible to creators

### Moderation
- Public lists may be subject to community guidelines
- Reporting mechanisms for inappropriate content
- Administrative tools for list management

## Performance Considerations

### Indexing
- Index on `user_id` for efficient user list queries
- Index on `user_list_id` for fast item retrieval
- Composite indexes for common query patterns

### Caching
- Cache popular public lists
- Cache user's own lists for quick access
- Invalidation strategies for list updates

## Future Enhancements

### Collaborative Features
- Shared editing permissions
- List forking and merging
- Comment systems for lists

### Advanced Organization
- Nested lists or folders
- Tag-based organization within lists
- Smart lists based on criteria

### Social Features
- List following and notifications
- Like/rating systems for lists
- User recommendations based on list similarity
