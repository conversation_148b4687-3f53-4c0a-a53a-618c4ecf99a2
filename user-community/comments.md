# Comments System

The comments system enables community discussion and feedback on various entities throughout the database.

## Overview

The comment system consists of two main entities:
- **comment**: The main comment record
- **comment_revision**: Historical revisions of comments

## comment Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique comment identifier |
| `content` | `String` | The comment text content |
| `state` | `CommentState` | Current state of the comment |
| `author_id` | `i32` | Foreign key to the user who wrote the comment |
| `target` | `CommentTarget` | Type of entity being commented on |
| `target_id` | `i32` | ID of the entity being commented on |
| `parent_id` | `Option<i32>` | Foreign key to parent comment (for replies) |
| `created_at` | `DateTimeWithTimeZone` | When the comment was created |
| `updated_at` | `DateTimeWithTimeZone` | When the comment was last modified |

### Comment States

| State | Description |
|-------|-------------|
| `Active` | Visible and normal comment |
| `Hidden` | Hidden by moderator but not deleted |
| `Deleted` | Soft-deleted comment |
| `Pending` | Awaiting moderation approval |

### Comment Targets

Comments can be attached to various entity types:
- **Artist**: Comments on artist pages
- **Release**: Comments on album/EP pages
- **Song**: Comments on individual tracks
- **Event**: Comments on convention/event pages
- **Tag**: Comments on tag definitions
- **Correction**: Comments on correction proposals
- **User**: Comments on user profiles

### Relationships

- **Belongs to**: `user` (comment author)
- **Self-referential**: `comment` (for reply threading)
- **Has many**: `comment_revision` (edit history)

## comment_revision Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique revision identifier |
| `comment_id` | `i32` | Foreign key to the parent comment |
| `content` | `String` | The comment content at this revision |
| `created_at` | `DateTimeWithTimeZone` | When this revision was created |

### Purpose

Revisions provide:
- **Edit history**: Track changes to comment content
- **Transparency**: Show what was changed and when
- **Moderation**: Ability to see original content
- **Accountability**: Prevent abuse through edit tracking

## Threading System

### Reply Structure
Comments support hierarchical threading through the `parent_id` field:
- **Top-level comments**: `parent_id` is `NULL`
- **Replies**: `parent_id` references the parent comment
- **Nested replies**: Can reply to replies, creating threads

### Threading Limits
- Maximum nesting depth may be limited for UI/UX reasons
- Deep threads may be collapsed or paginated
- Threading helps organize discussions logically

## Moderation Features

### Content Moderation
- **Automatic filtering**: Detect spam, inappropriate content
- **User reporting**: Community-driven moderation
- **Moderator tools**: Hide, delete, or edit comments
- **Appeal process**: Users can contest moderation actions

### State Management
- **Pending**: New comments may require approval
- **Hidden**: Temporarily hidden pending review
- **Deleted**: Soft deletion preserves data for audit
- **Active**: Normal, visible comments

## Database Schema

```sql
-- comment table
CREATE TABLE comment (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    state comment_state NOT NULL DEFAULT 'Active',
    author_id INTEGER NOT NULL REFERENCES user(id),
    target comment_target NOT NULL,
    target_id INTEGER NOT NULL,
    parent_id INTEGER REFERENCES comment(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- comment_revision table
CREATE TABLE comment_revision (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comment(id),
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## API Endpoints

### Comment Operations
- `POST /comments` - Create new comment
- `GET /comments/{id}` - Get comment details
- `PUT /comments/{id}` - Edit comment
- `DELETE /comments/{id}` - Delete comment

### Entity Comments
- `GET /{entity_type}/{id}/comments` - Get comments for entity
- `POST /{entity_type}/{id}/comments` - Add comment to entity

### Threading
- `GET /comments/{id}/replies` - Get replies to comment
- `POST /comments/{id}/replies` - Reply to comment

### Moderation
- `PUT /comments/{id}/state` - Change comment state
- `GET /comments/pending` - Get pending comments
- `POST /comments/{id}/report` - Report comment

## Features and Functionality

### Rich Text Support
- **Markdown**: Support for formatted text
- **Links**: Automatic link detection and formatting
- **Mentions**: User and entity mentions with notifications
- **Emoji**: Unicode emoji support

### Notifications
- **Reply notifications**: Notify when someone replies
- **Mention notifications**: Notify when mentioned
- **Moderation notifications**: Notify of moderation actions
- **Subscription**: Subscribe to comment threads

### Search and Discovery
- **Full-text search**: Search within comment content
- **Filter by author**: Find comments by specific users
- **Filter by entity**: Find comments on specific entities
- **Sort options**: By date, popularity, relevance

## Privacy and Security

### User Privacy
- **Anonymous comments**: Optional anonymous posting
- **Profile visibility**: Control comment visibility in profiles
- **Data export**: Users can export their comments
- **Account deletion**: Handle comments when users delete accounts

### Security Measures
- **Rate limiting**: Prevent comment spam
- **Content validation**: Sanitize input to prevent XSS
- **Authentication**: Verify user identity
- **Audit logging**: Track all comment operations

## Community Guidelines

### Content Policies
- **Respectful discourse**: Maintain civil discussion
- **Relevant content**: Keep comments on-topic
- **No spam**: Prevent promotional or repetitive content
- **Source attribution**: Credit sources when appropriate

### Enforcement
- **Warning system**: Progressive discipline for violations
- **Temporary restrictions**: Time-limited comment restrictions
- **Permanent bans**: For severe or repeated violations
- **Appeal process**: Fair review of moderation decisions
