# Images System

The images system manages all visual media in the Touhou Cloud DB, including storage, metadata, and associations with various entities.

## Overview

The image system centers around the **image** entity, which stores metadata and references for all images in the system, with support for multiple storage backends and entity associations.

## image Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique image identifier |
| `filename` | `String` | Original filename of the uploaded image |
| `directory` | `String` | Storage directory path |
| `uploaded_by` | `i32` | Foreign key to the user who uploaded the image |
| `uploaded_at` | `DateTimeWithTimeZone` | When the image was uploaded |
| `backend` | `StorageBackend` | Storage backend used for this image |

### Storage Backends

| Backend | Description |
|---------|-------------|
| `Local` | Local filesystem storage |
| `S3` | Amazon S3 or compatible object storage |
| `CDN` | Content delivery network storage |
| `Cloud` | Generic cloud storage provider |

### Relationships

- **Belongs to**: `user` (uploader)
- **Has many**: `artist_image` (artist associations)
- **Has many**: `release_image` (release associations)
- **Has many**: `image_queue` (queue entries)
- **Used by**: `user` (avatars and profile banners)

## Image Types and Usage

### Artist Images

Artists can have multiple types of associated images:
- **Profile pictures**: Primary artist avatar/photo
- **Performance photos**: Live performance documentation
- **Promotional images**: Marketing and promotional materials
- **Historical photos**: Archival documentation
- **Artwork**: Album covers and artistic representations

### Release Images

Releases support various image types:
- **Cover art**: Primary album/EP artwork
- **Back covers**: Reverse side artwork
- **Booklet pages**: Internal album documentation
- **Disc art**: CD/vinyl disc artwork
- **Promotional materials**: Marketing images
- **Alternative covers**: Regional or special edition covers

### User Images

Users can upload personal images:
- **Avatars**: Profile pictures
- **Profile banners**: Header images for user profiles
- **Custom images**: User-generated content

## File Management

### Storage Structure

Images are organized in a hierarchical directory structure:
```
images/
├── artists/
│   ├── ab/cd/abcd1234...xyz.jpg
│   └── ef/gh/efgh5678...xyz.png
├── releases/
│   ├── ij/kl/ijkl9012...xyz.jpg
│   └── mn/op/mnop3456...xyz.webp
└── users/
    ├── qr/st/qrst7890...xyz.jpg
    └── uv/wx/uvwx1234...xyz.png
```

### File Naming Convention

Files are renamed using a hash-based system:
- **Hash prefix**: First 4 characters create directory structure (ab/cd/)
- **Full hash**: Complete hash forms the filename
- **Extension preservation**: Original file extension is maintained
- **Collision handling**: Hash collisions are resolved with suffixes

### Supported Formats

| Format | Extension | Use Case |
|--------|-----------|----------|
| JPEG | `.jpg`, `.jpeg` | General photography, artwork |
| PNG | `.png` | Graphics with transparency, high quality |
| WebP | `.webp` | Modern format with better compression |
| GIF | `.gif` | Animated images (limited use) |

## Image Processing

### Automatic Processing

Upon upload, images undergo automatic processing:
- **Format validation**: Ensure supported file format
- **Size validation**: Check file size limits
- **Dimension validation**: Verify minimum/maximum dimensions
- **Content scanning**: Basic inappropriate content detection
- **Metadata extraction**: Extract EXIF and other metadata

### Optimization

Images are optimized for web delivery:
- **Compression**: Reduce file size while maintaining quality
- **Resizing**: Generate multiple sizes for different use cases
- **Format conversion**: Convert to optimal formats when beneficial
- **Progressive loading**: Enable progressive JPEG loading

### Thumbnail Generation

Multiple thumbnail sizes are generated:
- **Small**: 150x150px for lists and previews
- **Medium**: 300x300px for cards and galleries
- **Large**: 600x600px for detailed views
- **Original**: Full-size image for downloads

## Database Schema

```sql
-- image table
CREATE TABLE image (
    id SERIAL PRIMARY KEY,
    filename TEXT NOT NULL,
    directory TEXT NOT NULL,
    uploaded_by INTEGER NOT NULL REFERENCES user(id),
    uploaded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    backend storage_backend NOT NULL DEFAULT 'Local'
);

-- Entity association tables
CREATE TABLE artist_image (
    id SERIAL PRIMARY KEY,
    artist_id INTEGER NOT NULL REFERENCES artist(id),
    image_id INTEGER NOT NULL REFERENCES image(id),
    image_type artist_image_type NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    UNIQUE(artist_id, image_id)
);

CREATE TABLE release_image (
    id SERIAL PRIMARY KEY,
    release_id INTEGER NOT NULL REFERENCES release(id),
    image_id INTEGER NOT NULL REFERENCES image(id),
    image_type release_image_type NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    UNIQUE(release_id, image_id)
);
```

## API Endpoints

### Image Operations
- `POST /images` - Upload new image
- `GET /images/{id}` - Get image metadata
- `GET /images/{id}/file` - Download image file
- `DELETE /images/{id}` - Delete image (with permissions)

### Entity Images
- `GET /artists/{id}/images` - Get artist images
- `POST /artists/{id}/images` - Upload artist image
- `GET /releases/{id}/images` - Get release images
- `POST /releases/{id}/images` - Upload release image

### Image Variants
- `GET /images/{id}/thumbnail/{size}` - Get thumbnail
- `GET /images/{id}/optimized` - Get optimized version
- `GET /images/{id}/original` - Get original file

## Content Delivery

### CDN Integration

Images are delivered through content delivery networks:
- **Global distribution**: Serve images from geographically close servers
- **Caching**: Cache frequently accessed images
- **Bandwidth optimization**: Reduce server load
- **Performance improvement**: Faster image loading

### URL Structure

Image URLs follow a consistent pattern:
```
https://cdn.example.com/images/{directory}/{filename}
https://cdn.example.com/thumbnails/{size}/{directory}/{filename}
```

### Responsive Images

Support for responsive image delivery:
- **Multiple sizes**: Serve appropriate size based on device
- **Format negotiation**: Serve optimal format based on browser support
- **Lazy loading**: Load images only when needed
- **Progressive enhancement**: Graceful degradation for older browsers

## Security and Privacy

### Access Control

Image access is controlled through:
- **Public images**: Freely accessible to all users
- **Private images**: Restricted to authorized users
- **User images**: Controlled by user privacy settings
- **Moderated content**: Subject to approval process

### Content Validation

Security measures include:
- **File type validation**: Ensure only image files are uploaded
- **Content scanning**: Detect inappropriate or malicious content
- **Size limits**: Prevent abuse through oversized files
- **Rate limiting**: Prevent spam uploads

### Privacy Protection

User privacy is protected through:
- **Anonymization options**: Allow anonymous uploads where appropriate
- **Data retention**: Clear policies on image retention
- **Deletion rights**: Users can delete their uploaded images
- **GDPR compliance**: Comply with data protection regulations

## Performance Optimization

### Caching Strategy

Multi-level caching improves performance:
- **Browser cache**: Client-side caching with appropriate headers
- **CDN cache**: Edge server caching for global distribution
- **Application cache**: Server-side caching of metadata
- **Database cache**: Query result caching

### Storage Optimization

Efficient storage management:
- **Deduplication**: Avoid storing identical images multiple times
- **Compression**: Use efficient compression algorithms
- **Archival**: Move old images to cheaper storage tiers
- **Cleanup**: Remove orphaned or unused images

## Monitoring and Analytics

### Usage Metrics

Track image system usage:
- **Upload volume**: Number of images uploaded over time
- **Storage usage**: Total storage consumed
- **Bandwidth usage**: Data transfer for image delivery
- **Popular images**: Most frequently accessed images

### Performance Metrics

Monitor system performance:
- **Upload times**: Time to process and store images
- **Delivery times**: Image loading performance
- **Error rates**: Failed uploads or delivery attempts
- **Cache hit rates**: Effectiveness of caching strategies

## Quality Control

### Image Standards

Maintain quality standards:
- **Minimum resolution**: Ensure images meet minimum quality requirements
- **Aspect ratios**: Appropriate dimensions for intended use
- **File quality**: Balance between quality and file size
- **Content appropriateness**: Ensure images are suitable for the platform

### Moderation Process

Images go through moderation:
- **Automatic screening**: AI-based content detection
- **Human review**: Manual review for edge cases
- **Community reporting**: User-driven quality control
- **Appeal process**: Handle disputes over moderation decisions

## Best Practices

### For Users
- **High quality uploads**: Submit clear, high-resolution images
- **Appropriate content**: Ensure images are relevant and appropriate
- **Copyright respect**: Only upload images you have rights to use
- **Descriptive filenames**: Use meaningful filenames when possible

### For Developers
- **Efficient processing**: Optimize image processing pipelines
- **Graceful degradation**: Handle missing or failed images gracefully
- **Security first**: Implement robust security measures
- **Performance monitoring**: Continuously monitor and optimize performance

### For System Administrators
- **Regular backups**: Protect against data loss
- **Storage monitoring**: Track storage usage and costs
- **Performance tuning**: Optimize delivery and processing
- **Security updates**: Keep security measures current

## Future Enhancements

### Advanced Features
- **AI-powered tagging**: Automatic image content recognition
- **Duplicate detection**: Identify and handle duplicate images
- **Advanced editing**: In-browser image editing capabilities
- **Batch operations**: Bulk upload and management tools

### Integration Improvements
- **External storage**: Support for additional storage providers
- **API enhancements**: More sophisticated API capabilities
- **Mobile optimization**: Better mobile upload and viewing experience
- **Accessibility**: Improved accessibility features for images
