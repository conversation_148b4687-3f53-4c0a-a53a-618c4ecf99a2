# Image Queue System

The image queue system manages the review and approval process for user-submitted images, ensuring quality control and appropriate content moderation.

## Overview

The image queue system consists of the **image_queue** entity, which tracks images pending review, approval, or other processing states.

## image_queue Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique queue entry identifier |
| `image_id` | `Option<i32>` | Foreign key to the image (nullable during processing) |
| `status` | `ImageQueueStatus` | Current processing status |
| `handled_at` | `Option<DateTimeWithTimeZone>` | When the queue item was processed |
| `handled_by` | `Option<i32>` | User who processed the queue item |
| `reverted_at` | `Option<DateTimeWithTimeZone>` | When the item was reverted (if applicable) |
| `reverted_by` | `Option<i32>` | User who reverted the item |
| `created_at` | `DateTimeWithTimeZone` | When the queue entry was created |
| `creaded_by` | `i32` | User who created/uploaded the image |

### Queue Status

| Status | Description |
|--------|-------------|
| `Pending` | Awaiting review |
| `Approved` | Accepted and published |
| `Rejected` | Declined with reason |
| `Processing` | Currently being processed |
| `Reverted` | Previously approved but later reverted |

### Relationships

- **Belongs to**: `image` (the image being processed)
- **Belongs to**: `user` (creator, handler, reverter)
- **Has many**: `artist_image_queue` (artist-specific image queue entries)
- **Has many**: `release_image_queue` (release-specific image queue entries)

## Queue Workflow

### 1. Image Submission
- User uploads image through the system
- Image is stored but not immediately published
- Queue entry created with status `Pending`
- Automatic validation checks performed

### 2. Review Process
- Moderators review pending images
- Check for quality, appropriateness, and relevance
- May request changes or additional information
- Decision made to approve or reject

### 3. Approval/Rejection
- **Approved**: Image becomes publicly available
- **Rejected**: Image remains hidden, reason provided
- Status updated with timestamp and handler information
- Notifications sent to relevant users

### 4. Post-Approval Monitoring
- Community can report inappropriate content
- Moderators can revert previously approved images
- Reverted images return to pending or rejected status
- Full audit trail maintained

## Entity-Specific Queues

### Artist Image Queue

Artist images go through specialized review:
- **Profile pictures**: Artist avatars and promotional images
- **Performance photos**: Live performance documentation
- **Artwork**: Album covers and promotional materials
- **Historical photos**: Archival documentation

### Release Image Queue

Release images have specific requirements:
- **Cover art**: Primary album/EP artwork
- **Back covers**: Reverse side artwork
- **Booklet pages**: Internal documentation
- **Promotional materials**: Marketing images

## Quality Control

### Automatic Validation

Initial automated checks include:
- **File format**: Supported image formats (JPEG, PNG, WebP)
- **File size**: Within acceptable limits
- **Dimensions**: Minimum and maximum resolution requirements
- **Content scanning**: Basic inappropriate content detection

### Manual Review Criteria

Human reviewers evaluate:
- **Relevance**: Image relates to the associated entity
- **Quality**: Sufficient resolution and clarity
- **Appropriateness**: Follows community guidelines
- **Copyright**: No obvious copyright violations
- **Accuracy**: Correctly represents the entity

### Review Guidelines

#### Approval Criteria
- High quality and clear images
- Directly relevant to the entity
- Appropriate for all audiences
- No copyright concerns
- Proper aspect ratio and resolution

#### Rejection Reasons
- Poor image quality or resolution
- Irrelevant or incorrect content
- Inappropriate or offensive material
- Copyright violations
- Duplicate of existing image

## Database Schema

```sql
-- image_queue table
CREATE TABLE image_queue (
    id SERIAL PRIMARY KEY,
    image_id INTEGER REFERENCES image(id) ON DELETE SET NULL,
    status image_queue_status NOT NULL DEFAULT 'Pending',
    handled_at TIMESTAMPTZ,
    handled_by INTEGER REFERENCES user(id),
    reverted_at TIMESTAMPTZ,
    reverted_by INTEGER REFERENCES user(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    creaded_by INTEGER NOT NULL REFERENCES user(id)
);

-- Entity-specific queue tables
CREATE TABLE artist_image_queue (
    id SERIAL PRIMARY KEY,
    artist_id INTEGER NOT NULL REFERENCES artist(id),
    image_queue_id INTEGER NOT NULL REFERENCES image_queue(id),
    image_type artist_image_type NOT NULL,
    UNIQUE(artist_id, image_queue_id)
);

CREATE TABLE release_image_queue (
    id SERIAL PRIMARY KEY,
    release_id INTEGER NOT NULL REFERENCES release(id),
    image_queue_id INTEGER NOT NULL REFERENCES image_queue(id),
    image_type release_image_type NOT NULL,
    UNIQUE(release_id, image_queue_id)
);
```

## API Endpoints

### Queue Management
- `GET /image-queue` - List pending images (moderator)
- `GET /image-queue/{id}` - Get queue item details
- `POST /image-queue/{id}/approve` - Approve image
- `POST /image-queue/{id}/reject` - Reject image
- `POST /image-queue/{id}/revert` - Revert approved image

### User Operations
- `GET /users/{id}/image-queue` - User's submitted images
- `POST /artists/{id}/images` - Submit artist image
- `POST /releases/{id}/images` - Submit release image

### Statistics
- `GET /image-queue/stats` - Queue statistics
- `GET /image-queue/pending-count` - Count of pending items

## Moderation Tools

### Batch Operations
- **Bulk approval**: Approve multiple images at once
- **Bulk rejection**: Reject multiple images with same reason
- **Batch assignment**: Assign multiple items to specific moderators
- **Priority sorting**: Prioritize certain types of submissions

### Review Interface
- **Side-by-side comparison**: Compare with existing images
- **Metadata display**: Show image technical details
- **Entity context**: Display related entity information
- **History view**: Show previous submissions for same entity

### Moderation Workflow
- **Assignment system**: Distribute work among moderators
- **Escalation process**: Handle difficult decisions
- **Appeal mechanism**: Users can appeal rejections
- **Quality feedback**: Provide constructive feedback to users

## Performance Considerations

### Queue Processing
- **Prioritization**: Process high-priority items first
- **Load balancing**: Distribute review workload
- **Batch processing**: Handle multiple items efficiently
- **Background tasks**: Automated processing where possible

### Storage Management
- **Temporary storage**: Hold pending images separately
- **Cleanup processes**: Remove rejected images after retention period
- **Backup strategies**: Protect against data loss
- **CDN integration**: Efficient delivery of approved images

## Monitoring and Analytics

### Queue Metrics
- **Processing times**: Average time from submission to decision
- **Approval rates**: Percentage of images approved vs rejected
- **Moderator activity**: Individual moderator performance
- **Queue backlog**: Number of pending items over time

### Quality Indicators
- **Reversion rates**: How often approved images are later reverted
- **User satisfaction**: Feedback on moderation decisions
- **Appeal success rates**: Percentage of successful appeals
- **Community reports**: User reports on approved content

## User Experience

### Submission Process
- **Clear guidelines**: Provide clear image submission guidelines
- **Progress tracking**: Show users the status of their submissions
- **Feedback provision**: Explain rejection reasons clearly
- **Resubmission support**: Allow users to resubmit improved images

### Notification System
- **Status updates**: Notify users of approval/rejection decisions
- **Moderator alerts**: Alert moderators of new submissions
- **Escalation notifications**: Notify supervisors of escalated items
- **Community reports**: Alert moderators of reported content

## Security and Privacy

### Access Control
- **Role-based permissions**: Different access levels for different roles
- **Audit logging**: Track all moderation actions
- **Privacy protection**: Protect user information during review
- **Secure storage**: Ensure secure storage of pending images

### Content Safety
- **Automated scanning**: Use AI to detect inappropriate content
- **Human oversight**: Maintain human review for edge cases
- **Community reporting**: Enable community-driven content moderation
- **Legal compliance**: Ensure compliance with relevant laws

## Best Practices

### For Moderators
- **Consistent standards**: Apply guidelines consistently
- **Timely processing**: Process queue items promptly
- **Clear communication**: Provide clear feedback to users
- **Fair treatment**: Treat all submissions fairly and objectively

### For Users
- **Quality submissions**: Submit high-quality, relevant images
- **Follow guidelines**: Adhere to submission guidelines
- **Patience**: Allow time for review process
- **Constructive response**: Respond constructively to feedback

### For System Design
- **Scalable architecture**: Design for growing submission volumes
- **Efficient workflows**: Streamline the review process
- **User-friendly interfaces**: Make submission and review easy
- **Robust monitoring**: Track system performance and quality
