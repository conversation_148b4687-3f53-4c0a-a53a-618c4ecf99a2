# Label (厂牌)

唱片厂牌实体，代表唱片公司、音乐出版商和其他参与音乐制作、发行和推广的组织。

## 基本信息

| 字段                     | 类型          | 可选 | 说明              |
| ------------------------ | ------------- | ---- | ----------------- |
| id                       | INTEGER       | 否   | 唯一标识符 (主键) |
| name                     | TEXT          | 否   | 厂牌名称          |
| founded_date             | DATE          | 是   | 成立日期          |
| founded_date_precision   | DatePrecision | 否   | 成立日期精度枚举  |
| dissolved_date           | DATE          | 是   | 解散日期          |
| dissolved_date_precision | DatePrecision | 否   | 解散日期精度枚举  |

## 日期精度

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## 关联关系

### 直接关联 (一对多)

- **label_founder**: 创立或共同创立此厂牌的艺人
- **label_localized_name**: 不同语言的名称
- **release_catalog_number**: 此厂牌发行版本的目录编号

### 间接关联 (通过连接表)

- **artists**: 通过 `label_founder` 连接表（创始艺人）
- **releases**: 通过 `release_catalog_number` 连接表

## 业务逻辑

### 厂牌类型

虽然在实体中没有明确分类，但厂牌可以服务于各种功能：

- **唱片厂牌**: 主要发行和发行
- **出版商**: 版权管理和许可
- **发行商**: 实体和数字发行
- **子厂牌**: 大型母公司下的子厂牌
- **独立厂牌**: 艺人拥有或小型独立运营

### 生命周期管理

厂牌有完整的生命周期：

- **创立**: 厂牌何时以及由谁创立
- **活跃期**: 运营时间跨度
- **解散**: 厂牌何时停止运营（如适用）

### 创始人关系

系统追踪哪些艺人创立了厂牌，支持：

- **艺人拥有的厂牌**: 创立自己厂牌的音乐家
- **合作创立**: 多个艺人共同创立厂牌
- **历史背景**: 了解厂牌背后的起源和动机

## 相关实体

### 核心关联

- **创始人**: 建立厂牌的艺人
- **发行版本**: 在厂牌目录下发行的音乐
- **目录编号**: 官方发行标识符

### 元数据

- **本地化名称**: 支持多语言和多文字的厂牌名称
- **历史追踪**: 厂牌信息随时间的变更

### 发行管理

厂牌通过目录编号连接到发行版本，提供：

- **发行标识**: 官方目录编号系统
- **格式追踪**: 不同格式（CD、黑胶、数字）的单独编号
- **地区变化**: 不同市场的不同目录编号
- **重新发行管理**: 重新发行和重制版的新目录编号

## 使用示例

### 独立厂牌

```
name: "独立唱片"
founded_date: 2010-03-15
founded_date_precision: Day
dissolved_date: null
dissolved_date_precision: Day

相关数据:
- label_founder: 链接到创始艺人
- release_catalog_number: "IR001", "IR002", 等
```

### 历史厂牌

```
name: "经典唱片"
founded_date: 1955-01-01
founded_date_precision: Year
dissolved_date: 1985-01-01
dissolved_date_precision: Year

相关数据:
- release_catalog_number: 历史目录编号
- label_localized_name: 不同语言的名称
```

### 艺人创立的厂牌

```
name: "艺人自有厂牌"
founded_date: 2020-06-01
founded_date_precision: Month
dissolved_date: null

相关数据:
- label_founder: 创始艺人
- release_catalog_number: 艺人的发行版本和其他签约
```

### 多语言厂牌

```
name: "国际唱片"
founded_date: 1990-01-01
founded_date_precision: Year

相关数据:
- label_localized_name:
  - English: "International Records"
  - French: "Disques Internationaux"
  - German: "Internationale Schallplatten"
```

## 目录编号系统

厂牌通过 `release_catalog_number` 实体管理发行版本的目录编号：

### 编号方案

- **顺序编号**: IR001, IR002, IR003...
- **基于格式**: CD001, LP001, DIG001...
- **基于年份**: 2023-001, 2023-002...
- **基于系列**: ROCK-001, JAZZ-001...

### 多种格式

同一发行版本可以有多个目录编号：

- CD 版本: "LABEL-CD-001"
- 黑胶版本: "LABEL-LP-001"
- 数字版本: "LABEL-DIG-001"

### 地区变化

不同市场可能有不同的目录编号：

- 美国发行: "US-001"
- 欧洲发行: "EU-001"
- 日本发行: "JP-001"

## 厂牌层次

虽然没有明确建模，但系统可以通过以下方式表示厂牌关系：

### 母子厂牌

- **大型厂牌**: 拥有多个子厂牌的大型公司
- **子厂牌**: 大型厂牌下的专业化子厂牌
- **发行协议**: 由大型厂牌发行的独立厂牌

### 艺人关系

- **签约艺人**: 有录音合同的艺人
- **创始艺人**: 创立厂牌的艺人
- **发行艺人**: 发行版本由厂牌发行的艺人

## 历史背景

厂牌实体支持音乐产业历史：

### 时间线追踪

- **创立日期**: 厂牌何时建立
- **解散日期**: 厂牌何时停止运营
- **活跃期**: 厂牌的运营生命周期

### 产业演变

- **独立运动**: 艺人创立的厂牌
- **大型厂牌整合**: 合并和收购
- **数字化转型**: 新的发行模式

### 文化影响

- **流派发展**: 与特定音乐运动相关的厂牌
- **地区场景**: 支持地区音乐的本地厂牌
- **艺人发展**: 以培养人才著称的厂牌

## 与发行版本的集成

厂牌通过以下方式连接到更广泛的音乐生态系统：

### 发行管理

- **目录组织**: 系统化的编号和组织
- **格式管理**: 不同的实体和数字格式
- **重新发行计划**: 重新发行历史材料

### 艺人发展

- **名单管理**: 签约到厂牌的艺人
- **发行规划**: 协调艺人发行版本
- **推广和发行**: 营销和将音乐推向市场
