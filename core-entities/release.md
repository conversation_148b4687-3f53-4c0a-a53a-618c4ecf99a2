# Release (发行版本)

音乐发行版本，包括专辑、EP、单曲、合辑等音乐作品集合。

## 基本信息

| 字段                           | 类型          | 可选 | 说明              |
| ------------------------------ | ------------- | ---- | ----------------- |
| id                             | INTEGER       | 否   | 唯一标识符 (主键) |
| title                          | TEXT          | 否   | 发行版本标题      |
| release_type                   | ReleaseType   | 否   | 发行类型枚举      |
| release_date                   | DATE          | 是   | 正式发行日期      |
| release_date_precision         | DatePrecision | 否   | 发行日期精度枚举  |
| recording_date_start           | DATE          | 是   | 录制开始日期      |
| recording_date_start_precision | DatePrecision | 否   | 开始日期精度枚举  |
| recording_date_end             | DATE          | 是   | 录制结束日期      |
| recording_date_end_precision   | DatePrecision | 否   | 结束日期精度枚举  |

## 发行类型

- **Album** - 完整专辑（通常 8 首以上）
- **EP** - 迷你专辑（通常 3-7 首）
- **Single** - 单曲发行（可能包含 B 面歌曲）
- **Compilation** - 精选集或合辑
- **Demo** - 样本录音
- **Other** - 其他类型

## 日期精度

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## 关联关系

### 直接关联

- **release_artist** - 发行版本关联的艺人
- **release_catalog_number** - 不同厂牌的目录编号
- **release_credit** - 发行版本制作人员（制作人、工程师等）
- **release_event** - 相关活动（发行会、演出等）
- **release_image** - 封面和其他图片
- **release_image_queue** - 待审核的图片
- **release_localized_title** - 多语言标题
- **release_track** - 发行版本包含的曲目

### 间接关联

- **艺人** - 通过 release_artist 关联
- **活动**: 通过 `release_event` 连接表
- **图片**: 通过 `release_image` 连接表
- **歌曲**: 通过 `release_track` 连接表
- **厂牌**: 通过 `release_catalog_number` 连接表

## 业务逻辑

### 发行类型层次

系统区分不同类型的音乐发行版本：

- **专辑**: 主要发行版本，通常 8 首以上，代表重要的艺术声明
- **EP**: 较短的发行版本，通常 3-7 首，常用于专辑之间的新材料
- **单曲**: 专注于一首主打歌曲，可能包含混音版或 B 面歌曲
- **合辑**: 现有材料的集合、精选集或主题收藏
- **样本**: 粗糙的录音，通常未发行或限量发行
- **其他**: 涵盖边缘情况，如原声带、现场专辑或实验性发行

### 日期追踪

系统追踪多种日期类型：

1. **发行日期**: 正式公开发布的时间
2. **录制日期**: 音乐实际创作的时间
   - 支持在较长时期内录制的发行版本的日期范围
   - 对历史背景和制作时间线很有用

### 多格式支持

通过目录编号，发行版本可以有多种格式：

- 实体格式（CD、黑胶、磁带）
- 数字发行
- 不同地区的发行版本
- 重新发行和重制版

## 相关实体

### 核心关联

- **艺人**: 主要艺人、特邀艺人和贡献者
- **曲目**: 组成发行版本的个人歌曲
- **厂牌**: 发行或发行专辑的唱片厂牌
- **活动**: 发布活动、音乐会或演出

### 元数据

- **本地化标题**: 支持多语言的发行版本标题
- **图片**: 封面艺术、背面封面、内页说明、宣传图片
- **目录编号**: 来自厂牌的官方发行标识符
- **制作人员**: 详细的制作人员信息（制作人、工程师、录音室）

### 历史追踪

发行版本信息的变更通过历史表进行追踪，维护完整的审计轨迹。

## 使用示例

### 录音室专辑

```
title: "伟大的专辑"
release_type: Album
release_date: 2023-06-15
release_date_precision: Day
recording_date_start: 2022-10-01
recording_date_start_precision: Month
recording_date_end: 2023-02-28
recording_date_end_precision: Day
```

### 单曲发行

```
title: "热门歌曲"
release_type: Single
release_date: 2023-03-01
release_date_precision: Day
recording_date_start: 2023-01-15
recording_date_start_precision: Day
recording_date_end: 2023-01-15
recording_date_end_precision: Day
```

### 历史发行

```
title: "经典专辑"
release_type: Album
release_date: 1975-01-01
release_date_precision: Year
recording_date_start: 1974-01-01
recording_date_start_precision: Year
recording_date_end: 1974-01-01
recording_date_end_precision: Year
```

### 合辑

```
title: "精选集"
release_type: Compilation
release_date: 2023-12-01
release_date_precision: Day
recording_date_start: 2010-01-01
recording_date_start_precision: Year
recording_date_end: 2022-01-01
recording_date_end_precision: Year
```

## 曲目关系

发行版本通过 `release_track` 实体包含曲目，提供：

- 曲目排序和编号
- 曲目特定的元数据
- 多碟发行版本的碟片/面组织
- 个人曲目制作人员和信息

这允许复杂的发行版本结构，同时维护发行版本与其包含歌曲之间的关系。
