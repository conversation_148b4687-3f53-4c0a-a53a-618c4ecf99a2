# Song (歌曲)

歌曲实体，代表单独的音乐作品或曲目。歌曲是数据库中音乐内容的基本单位。

## 基本信息

| 字段  | 类型    | 可选 | 说明              |
| ----- | ------- | ---- | ----------------- |
| id    | INTEGER | 否   | 唯一标识符 (主键) |
| title | TEXT    | 否   | 歌曲标题          |

## 设计理念

歌曲实体采用极简的核心设计，大部分元数据存储在相关实体中。这种方法提供：

- **灵活性** - 可以在不改变核心结构的情况下添加额外属性
- **国际化** - 支持多语言标题和元数据
- **历史追踪** - 所有变更都在相关历史表中追踪
- **可扩展性** - 可以根据需要添加新类型的歌曲元数据

## 关联关系

### 直接关联 (一对多)

- **release_track**: 包含此歌曲的发行版本曲目
- **release_track_history**: 历史曲目关联
- **song_artist**: 与此歌曲关联的艺人
- **song_credit**: 此歌曲的制作人员（作词、作曲、制作人）
- **song_language**: 此歌曲演唱的语言
- **song_localized_title**: 不同语言的标题
- **song_lyrics_history**: 歌词的历史变更

### 直接关联 (一对一)

- **song_lyrics**: 此歌曲的当前歌词

### 间接关联 (通过连接表)

- **artists**: 通过 `song_artist` 连接表
- **releases**: 通过 `release_track` 连接表
- **languages**: 通过 `song_language` 连接表
- **credit_roles**: 通过 `song_credit` 连接表

## 业务逻辑

### 歌曲身份

歌曲代表抽象的音乐作品，独立于特定的录音或演出：

- 一首歌曲可以出现在多个发行版本中
- 不同的艺人可以演唱同一首歌曲
- 歌曲可以有多个版本（翻唱、混音、现场版本）
- 同一首歌曲在不同语言或地区可以有不同的标题

### 元数据组织

大部分歌曲元数据存储在相关实体中：

- **标题**: 本地化和替代标题
- **艺人**: 表演者、作曲者、作词者
- **制作人员**: 详细的贡献信息
- **语言**: 演出或歌词的语言
- **歌词**: 完整的歌词内容及历史追踪

### 版本处理

系统通过以下方式处理歌曲的不同版本：

- **发行版本曲目**: 发行版本上的特定录音
- **艺人关联**: 同一首歌曲的不同表演者
- **制作人员**: 不同版本的不同贡献者

## 相关实体

### 核心关联

- **艺人**: 作曲者、表演者、作词者和其他贡献者
- **发行版本**: 包含该歌曲的专辑、单曲和其他发行版本
- **制作人员**: 详细的基于角色的贡献追踪

### 内容和元数据

- **歌词**: 完整的歌词内容及版本控制
- **语言**: 演出和歌词的语言
- **本地化标题**: 多语言和多文字的歌曲标题

### 发行版本集成

- **发行版本曲目**: 歌曲在发行版本上的特定实例
  - 曲目编号和位置
  - 发行版本特定的元数据
  - 碟片和面的组织

## 使用示例

### 简单歌曲

```
id: 1
title: "示例歌曲"

相关数据:
- song_artist: 链接到作曲者和表演者
- song_lyrics: 包含歌曲的歌词
- release_track: 出现在"示例专辑"中
```

### 多语言歌曲

```
id: 2
title: "原始标题"

相关数据:
- song_localized_title:
  - English: "English Title"
  - Spanish: "Título en Español"
  - Japanese: "日本語のタイトル"
- song_language: English, Spanish
```

### 翻唱歌曲

```
原版:
id: 3
title: "经典歌曲"
- song_artist: 原创艺人 (作曲者)
- release_track: 原版专辑 (1970)

翻唱版本:
id: 3 (同一首歌)
title: "经典歌曲"
- song_artist: 翻唱艺人 (表演者)
- song_credit: 原创艺人 (作曲者), 翻唱艺人 (表演者)
- release_track: 翻唱专辑 (2023)
```

### 合作歌曲

```
id: 4
title: "合作作品"

相关数据:
- song_artist:
  - 艺人A (表演者)
  - 艺人B (表演者)
- song_credit:
  - 艺人A (作曲者, 作词者)
  - 艺人B (作曲者)
  - 制作人X (制作人)
  - 工程师Y (录音工程师)
```

## 歌词管理

歌曲可以通过 `song_lyrics` 实体管理歌词：

- **当前歌词**: 最新版本
- **历史追踪**: 所有以前的版本和变更
- **多语言支持**: 不同语言的歌词
- **版本控制**: 随时间追踪变更，提供完整的审计轨迹

## 曲目与歌曲的区别

系统中的重要区别：

- **歌曲**: 抽象的音乐作品
- **发行版本曲目**: 歌曲在发行版本上的特定录音/演出

这允许：

- 同一首歌曲的多个录音
- 不同的编曲或版本
- 对不同演出的正确署名
- 准确的发行版本追踪

## 与发行版本的集成

歌曲通过 `release_track` 实体连接到发行版本，提供：

- **曲目定位**: 曲目编号、碟片编号
- **发行版本特定数据**: 曲目特定的制作人员、注释
- **版本信息**: 不同的混音、编辑或编曲
- **艺人关联**: 曲目级别的艺人署名

这种设计支持复杂的场景，如：

- 来自不同来源曲目的合辑专辑
- 复杂组织的多碟发行版本
- 带有额外曲目的特别版
- 同一首歌曲不同演出的现场专辑
