# 系统枚举类型

系统中使用的所有枚举类型定义，提供标准化的分类和状态管理。

## 替代名称类型 (AlternativeNameType)

定义替代名称的类型：

- **Alias** - 别名
- **Localization** - 本地化名称

## 艺人图片类型 (ArtistImageType)

艺人图片的分类：

- **Profile** - 个人资料图片

## 艺人类型 (ArtistType)

艺人的基本分类：

- **Solo** - 个人艺人
- **Multiple** - 乐队/组合
- **Unknown** - 未知类型

## 评论状态 (CommentState)

评论的审核和显示状态：

- **Visable** - 可见（注：原文拼写）
- **InReview** - 审核中
- **Hidden** - 隐藏
- **Deleted** - 已删除

## 评论目标 (CommentTarget)

评论可以针对的对象类型：

- **Correction** - 修正

## 修正状态 (CorrectionStatus)

数据修正的处理状态：

- **Pending** - 待处理
- **Approved** - 已批准
- **Rejected** - 已拒绝

## 修正类型 (CorrectionType)

修正操作的类型：

- **Create** - 创建
- **Update** - 更新
- **Delete** - 删除

## 修正用户类型 (CorrectionUserType)

用户在修正过程中的角色：

- **Author** - 作者
- **Co-Author** - 共同作者
- **Reviewer** - 审核者
- **Approver** - 批准者

## 日期精度 (DatePrecision)

日期信息的精确程度：

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## 实体类型 (EntityType)

系统中的主要实体类型：

- **Artist** - 艺人
- **Label** - 厂牌
- **Release** - 发行版本
- **Song** - 歌曲
- **Tag** - 标签
- **Event** - 活动
- **SongLyrics** - 歌词
- **CreditRole** - 制作角色

## 图片队列状态 (ImageQueueStatus)

图片审核队列的状态：

- **Pending** - 待审核
- **Approved** - 已批准
- **Rejected** - 已拒绝
- **Cancelled** - 已取消
- **Reverted** - 已撤销

## 发行版本类型 (ReleaseType)

音乐发行版本的分类：

- **Album** - 专辑
- **EP** - 迷你专辑
- **Single** - 单曲
- **Compilation** - 合辑
- **Demo** - 样本
- **Other** - 其他

## 标签关系类型 (TagRelationType)

标签之间的关系类型：

- **Inherit** - 继承
- **Derive** - 派生

## 标签类型 (TagType)

标签的分类：

- **Descriptor** - 描述性标签
- **Genre** - 流派
- **Movement** - 音乐运动
- **Scene** - 音乐场景

## 发行版本图片类型 (ReleaseImageType)

发行版本图片的分类：

- **Cover** - 封面

## 存储后端 (StorageBackend)

文件存储后端类型：

- **Fs** - 文件系统

## 使用说明

### 日期精度的应用
日期精度枚举在整个系统中广泛使用：

```
艺人成立日期:
- Day: 2023-06-15 (确切日期)
- Month: 2023-06 (只知道月份)
- Year: 2023 (只知道年份)
```

### 修正工作流状态
修正系统使用多个枚举来管理工作流：

```
修正生命周期:
1. 创建修正 (CorrectionType: Create/Update/Delete)
2. 分配角色 (CorrectionUserType: Author/Reviewer/Approver)
3. 状态变更 (CorrectionStatus: Pending → Approved/Rejected)
```

### 图片管理流程
图片上传和审核使用状态枚举：

```
图片审核流程:
1. 上传 → Pending
2. 审核 → Approved/Rejected
3. 可能的操作 → Cancelled/Reverted
```

### 实体分类系统
实体类型枚举用于：

- **权限控制** - 基于实体类型的访问权限
- **搜索过滤** - 按实体类型筛选搜索结果
- **统计分析** - 按类型统计数据
- **工作流管理** - 不同实体类型的不同处理流程

### 标签系统层次
标签类型和关系支持复杂的分类系统：

```
标签层次示例:
Genre: Rock
  ├─ Inherit: Hard Rock
  ├─ Inherit: Progressive Rock
  └─ Derive: Alternative Rock

Movement: Grunge
  └─ Scene: Seattle Scene
```

### 多语言支持
替代名称类型支持国际化：

- **Alias** - 同语言的别名或昵称
- **Localization** - 不同语言的翻译名称

### 发行版本分类
发行版本类型帮助组织音乐内容：

- **Album** - 完整的艺术作品
- **EP** - 较短的发行版本
- **Single** - 主打歌曲发行
- **Compilation** - 精选或合辑
- **Demo** - 演示录音
- **Other** - 特殊或未分类的发行版本

### 评论管理
评论状态支持内容审核：

- **Visable** - 公开显示
- **InReview** - 等待审核
- **Hidden** - 暂时隐藏
- **Deleted** - 永久删除

这些枚举类型确保了系统的一致性和可维护性，同时为用户界面和业务逻辑提供了清晰的分类标准。
