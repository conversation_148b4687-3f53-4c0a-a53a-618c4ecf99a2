# Tags System

The tags system provides flexible categorization and metadata organization for music content, enabling rich discovery and classification capabilities.

## Overview

The tag system consists of two main entities:
- **tag**: The main tag definition
- **tag_alternative_name**: Alternative names and translations for tags

## tag Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique tag identifier |
| `name` | `String` | Primary name of the tag |
| `type` | `TagType` | Category/type of the tag |
| `short_description` | `String` | Brief description of the tag |
| `description` | `String` | Detailed description and usage guidelines |

### Tag Types

| Type | Description | Examples |
|------|-------------|----------|
| `Genre` | Musical genres and styles | "Electronic", "Rock", "Orchestral" |
| `Mood` | Emotional or atmospheric tags | "Energetic", "Melancholic", "Uplifting" |
| `Instrument` | Featured instruments | "Piano", "Violin", "Synthesizer" |
| `Vocal` | Vocal characteristics | "Female Vocals", "Choir", "Instrumental" |
| `Theme` | Thematic content | "Touhou", "Original", "Remix" |
| `Technical` | Technical aspects | "High Quality", "Remaster", "Demo" |
| `Event` | Event-related tags | "Comiket", "Reitaisai", "M3" |
| `Language` | Language-specific tags | "Japanese", "English", "Instrumental" |

### Relationships

- **Has many**: `tag_alternative_name` (alternative names)
- **Has many**: `tag_relation` (relationships with other tags)

## tag_alternative_name Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique alternative name identifier |
| `tag_id` | `i32` | Foreign key to the parent tag |
| `name` | `String` | Alternative name for the tag |
| `is_origin_language` | `bool` | Whether this is the original language name |
| `language_id` | `Option<i32>` | Foreign key to language (nullable) |

### Purpose

Alternative names provide:
- **Multilingual support**: Tags in different languages
- **Aliases**: Common alternative spellings or names
- **Historical names**: Previous names for evolved tags
- **Regional variants**: Different names in different regions

### Language Support

- **Original language**: Marked with `is_origin_language: true`
- **Translations**: Localized versions of tag names
- **Language-agnostic**: Some tags may not have specific languages
- **Fallback handling**: Display logic for missing translations

## Tag Relationships

### Hierarchical Structure

Tags can form hierarchical relationships:
- **Parent-child**: "Electronic" → "Trance" → "Uplifting Trance"
- **Broader-narrower**: General to specific categorization
- **Inheritance**: Child tags inherit properties from parents

### Relationship Types

| Type | Description | Example |
|------|-------------|---------|
| `Parent` | Broader category | "Electronic" is parent of "Trance" |
| `Child` | More specific category | "Vocal Trance" is child of "Trance" |
| `Related` | Associated but not hierarchical | "Piano" related to "Classical" |
| `Synonym` | Equivalent meaning | "EDM" synonym of "Electronic Dance Music" |
| `Antonym` | Opposite meaning | "Vocal" antonym of "Instrumental" |

## Tag Application

### Entity Tagging

Tags can be applied to various entities:
- **Songs**: Genre, mood, instruments, themes
- **Releases**: Overall style, production quality, event
- **Artists**: Musical style, origin, type
- **Events**: Location, type, focus

### Tagging Rules

- **Relevance**: Tags should be relevant to the content
- **Specificity**: Use most specific applicable tags
- **Consistency**: Apply tags consistently across similar content
- **Moderation**: Community review of tag applications

## Database Schema

```sql
-- tag table
CREATE TABLE tag (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    type tag_type NOT NULL,
    short_description TEXT NOT NULL,
    description TEXT NOT NULL
);

-- tag_alternative_name table
CREATE TABLE tag_alternative_name (
    id SERIAL PRIMARY KEY,
    tag_id INTEGER NOT NULL REFERENCES tag(id),
    name TEXT NOT NULL,
    is_origin_language BOOLEAN DEFAULT false,
    language_id INTEGER REFERENCES language(id)
);

-- tag_relation table (for hierarchies)
CREATE TABLE tag_relation (
    id SERIAL PRIMARY KEY,
    parent_tag_id INTEGER NOT NULL REFERENCES tag(id),
    child_tag_id INTEGER NOT NULL REFERENCES tag(id),
    relation_type tag_relation_type NOT NULL,
    UNIQUE(parent_tag_id, child_tag_id, relation_type)
);
```

## Tag Management

### Creation Process
1. **Proposal**: Users propose new tags
2. **Review**: Community reviews necessity and definition
3. **Approval**: Moderators approve well-defined tags
4. **Implementation**: Tag becomes available for use

### Quality Control
- **Definition clarity**: Clear, unambiguous descriptions
- **Uniqueness**: Avoid duplicate or redundant tags
- **Consistency**: Maintain consistent naming conventions
- **Scope**: Appropriate scope and specificity

### Maintenance
- **Merging**: Combine duplicate or similar tags
- **Splitting**: Divide overly broad tags
- **Deprecation**: Phase out obsolete tags
- **Updates**: Refine definitions and relationships

## Search and Discovery

### Tag-Based Search
- **Filter by tags**: Find content with specific tags
- **Tag combinations**: Search with multiple tag criteria
- **Exclusion**: Exclude content with certain tags
- **Weighted search**: Prioritize certain tag types

### Tag Suggestions
- **Auto-complete**: Suggest tags as users type
- **Related tags**: Suggest related or commonly used together
- **Popular tags**: Show frequently used tags
- **Contextual suggestions**: Suggest based on entity type

### Tag Clouds
- **Visual representation**: Show tag popularity visually
- **Interactive exploration**: Click tags to filter content
- **Hierarchical display**: Show tag relationships
- **Trending tags**: Highlight currently popular tags

## API Endpoints

### Tag Operations
- `GET /tags` - List all tags with filtering
- `GET /tags/{id}` - Get tag details
- `POST /tags` - Create new tag (with permissions)
- `PUT /tags/{id}` - Update tag information

### Tag Relationships
- `GET /tags/{id}/children` - Get child tags
- `GET /tags/{id}/parents` - Get parent tags
- `GET /tags/{id}/related` - Get related tags

### Entity Tagging
- `GET /{entity_type}/{id}/tags` - Get tags for entity
- `POST /{entity_type}/{id}/tags` - Add tags to entity
- `DELETE /{entity_type}/{id}/tags/{tag_id}` - Remove tag

### Search and Discovery
- `GET /tags/search` - Search tags by name
- `GET /tags/popular` - Get popular tags
- `GET /tags/suggestions` - Get tag suggestions

## Best Practices

### For Tag Creators
- **Clear definitions**: Write clear, unambiguous descriptions
- **Appropriate scope**: Neither too broad nor too narrow
- **Consistent naming**: Follow established naming conventions
- **Research existing**: Check for existing similar tags

### For Content Taggers
- **Accurate application**: Only apply relevant tags
- **Appropriate specificity**: Use most specific applicable tags
- **Consistent usage**: Apply tags consistently across similar content
- **Quality over quantity**: Better to have fewer accurate tags
