# Localization System

The localization system provides comprehensive multilingual support across all entities in the Touhou Cloud DB, enabling international accessibility and cultural adaptation.

## Overview

Localization in Touhou Cloud DB is implemented through a distributed approach where each major entity has its own localized name/title tables, all referencing the central `language` entity.

## Architecture

### Central Language Registry

The `language` entity serves as the central registry for all supported languages:
- **Standardized codes**: ISO 639-1/639-2 language codes
- **Display names**: Human-readable language names
- **Unique constraints**: Prevent duplicate language entries

### Distributed Localization Tables

Each localizable entity has its own dedicated localization table:

| Entity | Localization Table | Localized Field |
|--------|-------------------|----------------|
| Artist | `artist_localized_name` | Artist names |
| Release | `release_localized_title` | Album/EP titles |
| Song | `song_localized_title` | Track titles |
| Event | `event_alternative_name` | Event names |
| Label | `label_localized_name` | Label names |
| Tag | `tag_alternative_name` | Tag names |

### Common Localization Pattern

Most localization tables follow this standard pattern:

```sql
CREATE TABLE {entity}_localized_{field} (
    id SERIAL PRIMARY KEY,
    {entity}_id INTEGER NOT NULL REFERENCES {entity}(id),
    language_id INTEGER NOT NULL REFERENCES language(id),
    {field} TEXT NOT NULL,
    is_original BOOLEAN DEFAULT false,
    UNIQUE({entity}_id, language_id)
);
```

## Key Features

### Original Language Designation

Each localized entry can be marked as the original language:
- **is_original**: Boolean flag indicating source language
- **Source tracking**: Identify which version is the original
- **Translation hierarchy**: Establish translation relationships
- **Quality indicators**: Original versions often have higher accuracy

### Language Fallback System

The system implements intelligent fallback mechanisms:
1. **User preference**: Display in user's preferred language
2. **Original language**: Fall back to original if preferred unavailable
3. **English fallback**: Use English as universal fallback
4. **Entity default**: Use entity's primary name as last resort

### Multilingual Search

Search functionality spans across all localized versions:
- **Cross-language search**: Find content regardless of language
- **Romanization support**: Search Japanese content with romaji
- **Script conversion**: Handle different writing systems
- **Fuzzy matching**: Account for transliteration variations

## Implementation Details

### Database Design

#### Standard Localization Table
```sql
-- Example: Artist localized names
CREATE TABLE artist_localized_name (
    id SERIAL PRIMARY KEY,
    artist_id INTEGER NOT NULL REFERENCES artist(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES language(id),
    name TEXT NOT NULL,
    is_original BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(artist_id, language_id)
);

-- Indexes for performance
CREATE INDEX idx_artist_localized_name_artist_id ON artist_localized_name(artist_id);
CREATE INDEX idx_artist_localized_name_language_id ON artist_localized_name(language_id);
CREATE INDEX idx_artist_localized_name_is_original ON artist_localized_name(is_original);
```

#### Special Cases

**Tag Alternative Names** - Extended pattern with additional features:
```sql
CREATE TABLE tag_alternative_name (
    id SERIAL PRIMARY KEY,
    tag_id INTEGER NOT NULL REFERENCES tag(id),
    name TEXT NOT NULL,
    is_origin_language BOOLEAN DEFAULT false,
    language_id INTEGER REFERENCES language(id), -- Nullable for language-agnostic tags
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Song Languages** - Many-to-many relationship:
```sql
CREATE TABLE song_language (
    song_id INTEGER NOT NULL REFERENCES song(id),
    language_id INTEGER NOT NULL REFERENCES language(id),
    PRIMARY KEY (song_id, language_id)
);
```

### API Design

#### Language Negotiation
```http
GET /api/artists/123
Accept-Language: ja,en;q=0.9,zh;q=0.8

Response:
{
  "id": 123,
  "name": "ZUN",
  "localized_names": {
    "ja": "ZUN",
    "en": "ZUN",
    "zh": "ZUN"
  },
  "primary_language": "ja"
}
```

#### Explicit Language Selection
```http
GET /api/artists/123?lang=en
GET /api/artists/123?lang=ja,en  # Preference order
```

#### Bulk Localization
```http
GET /api/artists?lang=ja&include_localized=true
```

## Content Management

### Translation Workflow

1. **Content Creation**
   - Original content created in source language
   - Language automatically detected or manually specified
   - Marked as `is_original: true`

2. **Translation Request**
   - Community identifies translation needs
   - Translation tasks created and assigned
   - Priority based on community demand

3. **Translation Submission**
   - Contributors provide translations
   - Validation for format and basic quality
   - Submission through corrections system

4. **Review and Approval**
   - Native speakers review translations
   - Quality control and consistency checks
   - Approval through standard correction workflow

5. **Publication**
   - Approved translations become available
   - Cache invalidation and updates
   - Notification to interested users

### Quality Assurance

#### Validation Rules
- **Format consistency**: Maintain formatting across languages
- **Length constraints**: Reasonable length limits for UI compatibility
- **Character encoding**: Proper UTF-8 handling
- **Script validation**: Appropriate scripts for declared languages

#### Review Criteria
- **Accuracy**: Faithful representation of original meaning
- **Cultural appropriateness**: Culturally sensitive adaptations
- **Terminology consistency**: Consistent use of established terms
- **Grammar and style**: Proper language usage

## Special Language Considerations

### Japanese Content

**Script Handling**:
- **Hiragana/Katakana**: Native Japanese scripts
- **Kanji**: Chinese characters used in Japanese
- **Romaji**: Latin script representation
- **Mixed scripts**: Common in Japanese text

**Romanization Systems**:
- **Hepburn**: Most common for international use
- **Kunrei-shiki**: Official Japanese standard
- **Nihon-shiki**: Academic transliteration
- **Custom systems**: Artist-specific romanizations

### Chinese Variants

**Character Sets**:
- **Simplified Chinese**: Mainland China standard
- **Traditional Chinese**: Taiwan, Hong Kong, Macau
- **Regional differences**: Local terminology variations

**Input Methods**:
- **Pinyin**: Romanized input system
- **Zhuyin**: Phonetic symbols (Taiwan)
- **Stroke-based**: Character structure input

### European Languages

**Diacritics and Special Characters**:
- **Proper encoding**: Maintain accent marks and special characters
- **Search normalization**: Handle accented character searches
- **Sorting rules**: Language-appropriate collation

## Performance Optimization

### Caching Strategies

#### Multi-level Caching
1. **Application cache**: Frequently accessed localizations
2. **Database cache**: Query result caching
3. **CDN cache**: Static localized content
4. **Browser cache**: Client-side localization data

#### Cache Keys
```
localization:{entity_type}:{entity_id}:{language_code}
localization:fallback:{entity_type}:{entity_id}:{language_preference_chain}
```

### Database Optimization

#### Indexing Strategy
- **Composite indexes**: (entity_id, language_id) for fast lookups
- **Language indexes**: Quick filtering by language
- **Original language indexes**: Fast original content retrieval
- **Full-text indexes**: Multilingual search support

#### Query Optimization
```sql
-- Optimized query with fallback
WITH localized AS (
  SELECT name, language_id, is_original
  FROM artist_localized_name 
  WHERE artist_id = $1 
    AND language_id = ANY($2)  -- User's language preferences
),
fallback AS (
  SELECT name, language_id, is_original
  FROM artist_localized_name 
  WHERE artist_id = $1 
    AND is_original = true
)
SELECT COALESCE(localized.name, fallback.name) as name
FROM localized 
FULL OUTER JOIN fallback ON true
LIMIT 1;
```

## Monitoring and Analytics

### Localization Metrics
- **Coverage percentage**: How much content is localized per language
- **Translation velocity**: Speed of translation completion
- **Quality scores**: Community ratings of translations
- **Usage patterns**: Which languages are most requested

### Performance Metrics
- **Cache hit rates**: Effectiveness of caching strategies
- **Query performance**: Response times for localized content
- **Fallback frequency**: How often fallbacks are used
- **Error rates**: Failed localization attempts

## Future Enhancements

### Advanced Features
- **Machine translation integration**: AI-assisted translation suggestions
- **Translation memory**: Reuse of previous translations
- **Collaborative translation tools**: Real-time collaborative editing
- **Version control**: Track translation changes over time

### Accessibility Improvements
- **Screen reader optimization**: Proper language markup for assistive technology
- **Voice interface support**: Multilingual voice commands and responses
- **Keyboard layout support**: Input method optimization
- **Cultural accessibility**: Region-specific UI adaptations

## Best Practices

### For Developers
- **Unicode first**: Design with full Unicode support from the start
- **Graceful degradation**: Handle missing translations elegantly
- **Performance awareness**: Consider localization impact on performance
- **Testing coverage**: Test with various languages and scripts

### For Translators
- **Context understanding**: Understand the cultural context
- **Consistency maintenance**: Use established terminology
- **Quality focus**: Prioritize accuracy over speed
- **Community collaboration**: Work with other translators for consistency

### For System Administrators
- **Regular backups**: Protect translation data
- **Performance monitoring**: Watch for localization-related performance issues
- **Cache management**: Maintain efficient caching strategies
- **Quality monitoring**: Track translation quality metrics
