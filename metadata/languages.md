# Languages System

The language system provides comprehensive multilingual support throughout the database, enabling localized content and international accessibility.

## Overview

The language system centers around the **language** entity, which defines supported languages and enables localization across all other entities in the database.

## language Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique language identifier |
| `code` | `String` | ISO language code (e.g., "en", "ja", "zh") |
| `name` | `String` | Display name of the language |

### Constraints

- **Unique code**: Each language code must be unique
- **Unique name**: Each language name must be unique
- **Standard codes**: Follows ISO 639-1/639-2 standards

### Supported Languages

Common languages in the doujin music community:
- **Japanese** (`ja`): Primary language for most content
- **English** (`en`): International communication
- **Chinese** (`zh`): Traditional and Simplified variants
- **Korean** (`ko`): Growing doujin music scene
- **French** (`fr`): European community
- **German** (`de`): European community

## Localization Architecture

### Localized Name Entities

The system provides localized names for major entities:

| Entity | Localized Table | Purpose |
|--------|----------------|---------|
| Artist | `artist_localized_name` | Artist names in different languages |
| Release | `release_localized_title` | Album/EP titles in different languages |
| Song | `song_localized_title` | Track titles in different languages |
| Event | `event_alternative_name` | Event names in different languages |
| Label | `label_localized_name` | Label names in different languages |
| Tag | `tag_alternative_name` | Tag names in different languages |

### Common Localization Pattern

Most localized entities follow this pattern:

```sql
CREATE TABLE {entity}_localized_name (
    id SERIAL PRIMARY KEY,
    {entity}_id INTEGER NOT NULL REFERENCES {entity}(id),
    language_id INTEGER NOT NULL REFERENCES language(id),
    name TEXT NOT NULL,
    is_original BOOLEAN DEFAULT false
);
```

### Special Cases

#### Tag Alternative Names
Tags use a slightly different pattern with additional fields:
- `is_origin_language`: Indicates if this is the original language
- `language_id`: Optional, may be null for language-agnostic tags

#### Song Languages
Songs have a many-to-many relationship with languages through `song_language`:
- Tracks can be performed in multiple languages
- Instrumental tracks may have no associated languages
- Covers may change the language from the original

## Language Detection and Assignment

### Automatic Detection
- **Script analysis**: Detect writing systems (Latin, Hiragana, Kanji, etc.)
- **Content analysis**: Analyze text patterns and common words
- **User input**: Allow manual language specification
- **Fallback rules**: Default assignments when detection fails

### Manual Override
- Users can specify languages explicitly
- Corrections system allows language updates
- Multiple languages can be assigned to single entities
- Original language designation for translations

## Internationalization Features

### User Interface
- **Language preferences**: Users can set preferred languages
- **Fallback chains**: Display content in preferred order
- **Mixed content**: Handle multilingual content gracefully
- **RTL support**: Right-to-left language support

### Content Display
- **Primary language**: Show main language version
- **Alternative languages**: List available translations
- **Language indicators**: Visual cues for language
- **Translation status**: Show completeness of translations

## Database Schema

```sql
-- language table
CREATE TABLE language (
    id SERIAL PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL UNIQUE
);

-- Example localized name table
CREATE TABLE artist_localized_name (
    id SERIAL PRIMARY KEY,
    artist_id INTEGER NOT NULL REFERENCES artist(id),
    language_id INTEGER NOT NULL REFERENCES language(id),
    name TEXT NOT NULL,
    is_original BOOLEAN DEFAULT false,
    UNIQUE(artist_id, language_id)
);

-- Song language relationship
CREATE TABLE song_language (
    song_id INTEGER NOT NULL REFERENCES song(id),
    language_id INTEGER NOT NULL REFERENCES language(id),
    PRIMARY KEY (song_id, language_id)
);
```

## API Considerations

### Language Negotiation
- **Accept-Language header**: HTTP standard language preferences
- **Query parameters**: Explicit language selection
- **User preferences**: Stored language settings
- **Default fallbacks**: Sensible defaults when preferences unavailable

### Content Delivery
- **Localized responses**: Return content in requested language
- **Multiple languages**: Include alternative language versions
- **Language metadata**: Indicate available languages
- **Translation status**: Show completeness information

## Content Management

### Translation Workflow
1. **Original content**: Created in source language
2. **Translation requests**: Community identifies translation needs
3. **Translation submission**: Contributors provide translations
4. **Review process**: Quality control for translations
5. **Publication**: Approved translations become available

### Quality Control
- **Native speaker review**: Translations reviewed by native speakers
- **Consistency checks**: Terminology consistency across translations
- **Cultural adaptation**: Localization beyond literal translation
- **Update synchronization**: Keep translations current with source

## Special Considerations

### Japanese Content
- **Multiple scripts**: Hiragana, Katakana, Kanji, Romaji
- **Reading variants**: Multiple pronunciations for same text
- **Romanization**: Standardized romanization systems
- **Cultural context**: Japanese-specific cultural references

### Chinese Variants
- **Traditional vs Simplified**: Different character sets
- **Regional differences**: Mainland, Taiwan, Hong Kong variants
- **Pronunciation systems**: Pinyin, Zhuyin, etc.
- **Cultural variations**: Regional cultural differences

### Transliteration
- **Romanization**: Converting non-Latin scripts to Latin
- **Standardization**: Consistent transliteration systems
- **Search optimization**: Enable searching across scripts
- **User preferences**: Allow different romanization preferences

## Performance Optimization

### Caching Strategies
- **Language-specific caches**: Cache content by language
- **Fallback caching**: Cache fallback language chains
- **Translation caching**: Cache translation lookups
- **User preference caching**: Cache user language settings

### Database Optimization
- **Indexing**: Efficient language-based queries
- **Partitioning**: Partition by language for large datasets
- **Denormalization**: Strategic denormalization for performance
- **Query optimization**: Optimize multilingual queries

## Best Practices

### For Developers
- **Unicode support**: Proper UTF-8 handling throughout
- **Collation rules**: Language-appropriate sorting
- **Text direction**: Handle RTL and LTR text
- **Font support**: Ensure proper font coverage

### For Content Contributors
- **Accurate translations**: Focus on meaning over literal translation
- **Cultural sensitivity**: Respect cultural differences
- **Consistency**: Maintain terminology consistency
- **Source attribution**: Credit original sources appropriately
