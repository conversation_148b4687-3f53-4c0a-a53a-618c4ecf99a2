# 实体关系图

THCDB 系统中各实体之间的关系概览，展示了复杂的音乐数据库架构。

## 核心实体关系

### 音乐内容层次
```
Artist (艺人)
    ├─ 创作 → Song (歌曲)
    ├─ 发行 → Release (发行版本)
    └─ 创立 → Label (厂牌)

Release (发行版本)
    ├─ 包含 → Song (通过 release_track)
    ├─ 关联 → Artist (通过 release_artist)
    ├─ 发行于 → Label (通过 release_catalog_number)
    └─ 关联 → Event (通过 release_event)

Song (歌曲)
    ├─ 演唱者 → Artist (通过 song_artist)
    ├─ 出现在 → Release (通过 release_track)
    └─ 制作人员 → Artist (通过 song_credit)
```

## 详细关系映射

### Artist (艺人) 的关系网络

#### 直接关系
- **artist_alias_history** - 别名历史
- **artist_image** - 艺人图片
- **artist_image_queue** - 待审核图片
- **artist_link** - 外部链接
- **artist_localized_name** - 多语言名称
- **artist_membership_history** - 成员关系历史

#### 创作关系
- **song_artist** - 参与的歌曲
- **song_credit** - 歌曲制作角色
- **release_artist** - 参与的发行版本
- **release_credit** - 发行版本制作角色
- **release_track_artist** - 参与的具体曲目

#### 商业关系
- **label_founder** - 创立的厂牌

### Release (发行版本) 的关系网络

#### 内容组织
- **release_track** - 包含的曲目
- **release_artist** - 参与艺人
- **release_credit** - 制作人员

#### 商业信息
- **release_catalog_number** - 目录编号
- **release_event** - 相关活动

#### 媒体资源
- **release_image** - 封面等图片
- **release_image_queue** - 待审核图片

#### 本地化
- **release_localized_title** - 多语言标题

### Song (歌曲) 的关系网络

#### 创作信息
- **song_artist** - 参与艺人
- **song_credit** - 制作人员角色

#### 内容信息
- **song_lyrics** - 歌词内容
- **song_language** - 演唱语言
- **song_localized_title** - 多语言标题

#### 发行信息
- **release_track** - 出现的发行版本

### Label (厂牌) 的关系网络

#### 组织信息
- **label_founder** - 创始人
- **label_localized_name** - 多语言名称

#### 发行管理
- **release_catalog_number** - 发行的版本

### Event (活动) 的关系网络

#### 活动信息
- **event_alternative_name** - 替代名称

#### 关联内容
- **release_event** - 相关发行版本

## 连接表 (Junction Tables)

### 艺人关联
- **song_artist** - 歌曲 ↔ 艺人
- **release_artist** - 发行版本 ↔ 艺人
- **release_track_artist** - 曲目 ↔ 艺人
- **label_founder** - 厂牌 ↔ 艺人

### 内容关联
- **release_track** - 发行版本 ↔ 歌曲
- **release_event** - 发行版本 ↔ 活动
- **release_catalog_number** - 发行版本 ↔ 厂牌

### 制作关联
- **song_credit** - 歌曲 ↔ 艺人 ↔ 角色
- **release_credit** - 发行版本 ↔ 艺人 ↔ 角色

### 媒体关联
- **artist_image** - 艺人 ↔ 图片
- **release_image** - 发行版本 ↔ 图片

## 用户和社区关系

### User (用户) 中心
```
User (用户)
    ├─ 创建 → Comment (评论)
    ├─ 提交 → Correction (修正)
    ├─ 管理 → UserList (用户列表)
    ├─ 关注 → User (通过 user_following)
    └─ 拥有 → UserRole (用户角色)
```

### 协作系统
- **correction** - 数据修正
- **correction_revision** - 修正版本
- **correction_user** - 修正参与者
- **comment** - 评论系统
- **comment_revision** - 评论版本

### 个人化功能
- **user_list** - 用户列表
- **user_list_item** - 列表项目
- **user_following** - 关注关系

## 元数据和支持系统

### 本地化支持
- **artist_localized_name** - 艺人多语言名称
- **release_localized_title** - 发行版本多语言标题
- **song_localized_title** - 歌曲多语言标题
- **label_localized_name** - 厂牌多语言名称
- **language** - 语言定义

### 标签系统
- **tag** - 标签
- **tag_alternative_name** - 标签替代名称
- **tag_relation** - 标签关系

### 角色和权限
- **role** - 系统角色
- **user_role** - 用户角色分配
- **credit_role** - 制作角色
- **credit_role_inheritance** - 角色继承

### 媒体管理
- **image** - 图片资源
- **image_queue** - 图片审核队列
- **artist_image_queue** - 艺人图片队列
- **release_image_queue** - 发行版本图片队列

## 关系模式

### 一对多关系 (1:N)
- Artist → Songs (通过 song_artist)
- Artist → Releases (通过 release_artist)
- Release → Tracks (通过 release_track)
- User → Comments
- User → Lists

### 多对多关系 (M:N)
- Artists ↔ Songs (通过 song_artist)
- Artists ↔ Releases (通过 release_artist)
- Releases ↔ Events (通过 release_event)
- Songs ↔ Languages (通过 song_language)
- Users ↔ Users (通过 user_following)

### 层次关系
- Tag → Tag (通过 tag_relation)
- CreditRole → CreditRole (通过 credit_role_inheritance)
- Artist → Artist (通过 artist_membership)

## 数据流向

### 内容创建流程
```
1. Artist 创建 Song
2. Song 组织成 Release
3. Release 通过 Label 发行
4. Event 推广 Release
5. User 发现和评论内容
```

### 协作编辑流程
```
1. User 提交 Correction
2. 其他 User 审核和讨论
3. 批准后更新主实体
4. 记录到 History 表
```

### 媒体管理流程
```
1. 上传到 ImageQueue
2. 审核和批准
3. 关联到 Artist/Release
4. 在 Image 表中存储
```

这个关系网络展示了 THCDB 作为一个复杂音乐数据库的丰富性和灵活性，支持从基本的音乐目录到高级的社区协作功能。
